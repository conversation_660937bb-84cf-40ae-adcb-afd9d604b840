"""
Order Spoofing Detection Algorithm
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any
import logging

from detection.base_detector import BaseDetector
from models.data_models import ManipulationSignal, OptionsData, PatternType
from config.settings import settings
from utils.enhanced_logging import enhanced_logger
from utils.adaptive_thresholds import adaptive_threshold_manager

logger = logging.getLogger(__name__)

class SpoofingDetector(BaseDetector):
    """
    Detects order spoofing patterns - large orders placed and quickly cancelled
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("spoofing_detector", config)

        # Configuration parameters - now using adaptive thresholds
        self.use_adaptive_thresholds = self.config.get("use_adaptive_thresholds", True)

        # Fallback static thresholds (used when adaptive system is warming up)
        self.static_qty_threshold = self.config.get(
            "qty_threshold",
            settings.detection.spoof_qty_threshold
        )
        self.time_window = self.config.get(
            "time_window_seconds",
            settings.detection.spoof_time_window_seconds
        )
        self.static_min_price_impact = self.config.get("min_price_impact", 0.01)
        self.confidence_base = self.config.get("confidence_base", 0.6)
        
    def get_required_data_window(self) -> int:
        """Minimum data points needed for spoofing detection"""
        return 20  # Need at least 20 data points to detect patterns
    
    async def detect(self, data: List[OptionsData]) -> List[ManipulationSignal]:
        """
        Detect order spoofing patterns

        Args:
            data: List of options data to analyze

        Returns:
            List of detected spoofing signals
        """
        signals = []

        try:
            # Update adaptive thresholds with current market data
            if self.use_adaptive_thresholds:
                adaptive_threshold_manager.update_market_data(data)

                # Check if we should trade in current regime
                if not adaptive_threshold_manager.should_trade_in_regime():
                    enhanced_logger.logger.warning(
                        f"[REGIME FILTER] Skipping detection in {adaptive_threshold_manager.current_regime} regime"
                    )
                    return signals

            # Get current thresholds (adaptive or static)
            current_thresholds = self._get_current_thresholds()

            # Log current market data transparently
            enhanced_logger.log_current_prices(data)

            # Log detection start with current thresholds
            enhanced_logger.log_detection_calculation(
                "Spoofing Detector",
                len(data),
                current_thresholds
            )

            # Convert to DataFrame for easier analysis
            df = self._prepare_dataframe(data)

            if df.empty:
                enhanced_logger.logger.warning("[WARN] No valid data for spoofing detection")
                return signals
            
            # Group by symbol, strike, and option type
            for (symbol, strike, option_type), group in df.groupby(['symbol', 'strike', 'option_type']):
                if len(group) < 10:  # Need minimum data points
                    continue

                # Log analysis for this option
                enhanced_logger.logger.info(f"[ANALYZE] Analyzing {symbol} {strike} {option_type}")
                enhanced_logger.logger.info(f"   Data points: {len(group)}")
                enhanced_logger.logger.info(f"   Price range: Rs{group['last_price'].min():.2f} - Rs{group['last_price'].max():.2f}")
                enhanced_logger.logger.info(f"   Volume range: {group['volume'].min():,} - {group['volume'].max():,}")
                enhanced_logger.logger.info(f"   Bid qty range: {group['bid_qty'].min():,} - {group['bid_qty'].max():,}")

                # Sort by timestamp
                group = group.sort_values('timestamp').reset_index(drop=True)

                # Detect spoofing patterns in this group
                spoof_signals = self._detect_spoofing_in_group(
                    group, symbol, strike, option_type
                )
                signals.extend(spoof_signals)

            enhanced_logger.logger.info(f"[OK] Spoofing detector completed: {len(signals)} signals found")
            
        except Exception as e:
            logger.error(f"Error in spoofing detection: {str(e)}")
            raise
        
        return signals

    def _get_current_thresholds(self) -> Dict[str, Any]:
        """Get current thresholds (adaptive or static fallback)"""
        if self.use_adaptive_thresholds:
            return {
                "qty_threshold": adaptive_threshold_manager.get_threshold("spoof_qty_threshold") or self.static_qty_threshold,
                "time_window_seconds": self.time_window,
                "min_price_impact": adaptive_threshold_manager.get_threshold("spoof_price_impact_threshold") or self.static_min_price_impact,
                "confidence_base": self.confidence_base,
                "regime": adaptive_threshold_manager.current_regime,
                "adaptive_mode": True
            }
        else:
            return {
                "qty_threshold": self.static_qty_threshold,
                "time_window_seconds": self.time_window,
                "min_price_impact": self.static_min_price_impact,
                "confidence_base": self.confidence_base,
                "adaptive_mode": False
            }

    @property
    def qty_threshold(self) -> float:
        """Get current quantity threshold"""
        if self.use_adaptive_thresholds:
            return adaptive_threshold_manager.get_threshold("spoof_qty_threshold") or self.static_qty_threshold
        return self.static_qty_threshold

    @property
    def min_price_impact(self) -> float:
        """Get current minimum price impact threshold"""
        if self.use_adaptive_thresholds:
            return adaptive_threshold_manager.get_threshold("spoof_price_impact_threshold") or self.static_min_price_impact
        return self.static_min_price_impact

    def _prepare_dataframe(self, data: List[OptionsData]) -> pd.DataFrame:
        """
        Convert options data to DataFrame for analysis
        
        Args:
            data: List of options data
            
        Returns:
            Prepared DataFrame
        """
        records = []
        for option in data:
            records.append({
                'symbol': option.symbol,
                'strike': option.strike,
                'option_type': option.option_type.value,
                'timestamp': option.timestamp,
                'last_price': option.last_price,
                'bid_price': option.bid_price,
                'ask_price': option.ask_price,
                'bid_qty': option.bid_qty,
                'ask_qty': option.ask_qty,
                'volume': option.volume,
                'open_interest': option.open_interest
            })
        
        df = pd.DataFrame(records)
        
        if not df.empty:
            # Ensure timestamp is datetime
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # Calculate derived metrics
            df['spread'] = df['ask_price'] - df['bid_price']
            df['mid_price'] = (df['bid_price'] + df['ask_price']) / 2
            df['total_qty'] = df['bid_qty'] + df['ask_qty']
            
        return df
    
    def _detect_spoofing_in_group(
        self, 
        group: pd.DataFrame, 
        symbol: str, 
        strike: float, 
        option_type: str
    ) -> List[ManipulationSignal]:
        """
        Detect spoofing patterns within a specific option group
        
        Args:
            group: DataFrame for specific option
            symbol: Symbol name
            strike: Strike price
            option_type: Option type (CE/PE)
            
        Returns:
            List of spoofing signals
        """
        signals = []
        
        # Calculate quantity changes
        group['bid_qty_change'] = group['bid_qty'].diff()
        group['ask_qty_change'] = group['ask_qty'].diff()
        group['price_change'] = group['last_price'].diff()
        
        # Look for spoofing patterns
        for i in range(1, len(group) - 1):
            current = group.iloc[i]
            prev = group.iloc[i - 1]
            next_row = group.iloc[i + 1]
            
            # Check time window
            time_diff = (next_row['timestamp'] - current['timestamp']).total_seconds()
            if time_diff > self.time_window:
                continue
            
            # Pattern 1: Large bid quantity spike followed by removal
            bid_spoof = self._check_bid_spoofing(current, next_row)
            if bid_spoof:
                signal = self._create_spoofing_signal(
                    current, next_row, symbol, strike, option_type, "bid_spoofing", bid_spoof
                )
                signals.append(signal)
            
            # Pattern 2: Large ask quantity spike followed by removal
            ask_spoof = self._check_ask_spoofing(current, next_row)
            if ask_spoof:
                signal = self._create_spoofing_signal(
                    current, next_row, symbol, strike, option_type, "ask_spoofing", ask_spoof
                )
                signals.append(signal)
            
            # Pattern 3: Coordinated bid-ask spoofing
            coordinated_spoof = self._check_coordinated_spoofing(current, next_row)
            if coordinated_spoof:
                signal = self._create_spoofing_signal(
                    current, next_row, symbol, strike, option_type, "coordinated_spoofing", coordinated_spoof
                )
                signals.append(signal)
        
        return signals
    
    def _check_bid_spoofing(self, current: pd.Series, next_row: pd.Series) -> Dict[str, Any]:
        """
        Check for bid spoofing pattern
        
        Args:
            current: Current data point
            next_row: Next data point
            
        Returns:
            Spoofing details if detected, None otherwise
        """
        # Large bid quantity increase followed by decrease
        bid_increase = current['bid_qty_change'] > self.qty_threshold
        bid_decrease = next_row['bid_qty_change'] < -self.qty_threshold
        
        # Price impact check
        price_impact = abs(next_row['price_change']) if not pd.isna(next_row['price_change']) else 0
        
        if bid_increase and bid_decrease and price_impact >= self.min_price_impact:
            # Log the detected pattern with full transparency
            enhanced_logger.logger.warning(f"[ALERT] BID SPOOFING PATTERN DETECTED!")
            enhanced_logger.logger.warning(f"   Current bid qty change: +{current['bid_qty_change']:,} lots")
            enhanced_logger.logger.warning(f"   Next bid qty change: {next_row['bid_qty_change']:,} lots")
            enhanced_logger.logger.warning(f"   Price impact: Rs{price_impact:.2f}")
            enhanced_logger.logger.warning(f"   Time window: {(next_row['timestamp'] - current['timestamp']).total_seconds():.1f}s")

            return {
                "type": "bid_spoofing",
                "qty_spike": current['bid_qty_change'],
                "qty_removal": next_row['bid_qty_change'],
                "price_impact": price_impact,
                "time_window": (next_row['timestamp'] - current['timestamp']).total_seconds()
            }
        
        return None
    
    def _check_ask_spoofing(self, current: pd.Series, next_row: pd.Series) -> Dict[str, Any]:
        """
        Check for ask spoofing pattern
        
        Args:
            current: Current data point
            next_row: Next data point
            
        Returns:
            Spoofing details if detected, None otherwise
        """
        # Large ask quantity increase followed by decrease
        ask_increase = current['ask_qty_change'] > self.qty_threshold
        ask_decrease = next_row['ask_qty_change'] < -self.qty_threshold
        
        # Price impact check
        price_impact = abs(next_row['price_change']) if not pd.isna(next_row['price_change']) else 0
        
        if ask_increase and ask_decrease and price_impact >= self.min_price_impact:
            return {
                "type": "ask_spoofing",
                "qty_spike": current['ask_qty_change'],
                "qty_removal": next_row['ask_qty_change'],
                "price_impact": price_impact,
                "time_window": (next_row['timestamp'] - current['timestamp']).total_seconds()
            }
        
        return None
    
    def _check_coordinated_spoofing(self, current: pd.Series, next_row: pd.Series) -> Dict[str, Any]:
        """
        Check for coordinated bid-ask spoofing
        
        Args:
            current: Current data point
            next_row: Next data point
            
        Returns:
            Spoofing details if detected, None otherwise
        """
        # Both bid and ask quantities spike and then get removed
        bid_spike = abs(current['bid_qty_change']) > self.qty_threshold / 2
        ask_spike = abs(current['ask_qty_change']) > self.qty_threshold / 2
        
        bid_removal = abs(next_row['bid_qty_change']) > self.qty_threshold / 2
        ask_removal = abs(next_row['ask_qty_change']) > self.qty_threshold / 2
        
        # Check if spikes and removals are in opposite directions
        bid_pattern = (current['bid_qty_change'] > 0 and next_row['bid_qty_change'] < 0) or \
                     (current['bid_qty_change'] < 0 and next_row['bid_qty_change'] > 0)
        
        ask_pattern = (current['ask_qty_change'] > 0 and next_row['ask_qty_change'] < 0) or \
                     (current['ask_qty_change'] < 0 and next_row['ask_qty_change'] > 0)
        
        if bid_spike and ask_spike and bid_removal and ask_removal and bid_pattern and ask_pattern:
            return {
                "type": "coordinated_spoofing",
                "bid_qty_change": current['bid_qty_change'],
                "ask_qty_change": current['ask_qty_change'],
                "bid_removal": next_row['bid_qty_change'],
                "ask_removal": next_row['ask_qty_change'],
                "time_window": (next_row['timestamp'] - current['timestamp']).total_seconds()
            }
        
        return None
    
    def _create_spoofing_signal(
        self,
        current: pd.Series,
        next_row: pd.Series,
        symbol: str,
        strike: float,
        option_type: str,
        spoof_type: str,
        spoof_details: Dict[str, Any]
    ) -> ManipulationSignal:
        """
        Create a manipulation signal for detected spoofing
        
        Args:
            current: Current data point
            next_row: Next data point
            symbol: Symbol name
            strike: Strike price
            option_type: Option type
            spoof_type: Type of spoofing detected
            spoof_details: Details of the spoofing pattern
            
        Returns:
            ManipulationSignal instance
        """
        # Calculate confidence based on various factors
        confidence = self._calculate_confidence(spoof_details, current, next_row)
        
        # Estimate profit impact
        estimated_profit = self._estimate_profit(spoof_details, current)
        
        # Create description
        description = self._create_description(spoof_type, spoof_details, symbol, strike, option_type)

        # Calculate profit breakdown for transparency
        calculation_breakdown = {
            "base_quantity_impact": max(
                abs(spoof_details.get("qty_spike", 0)),
                abs(spoof_details.get("bid_qty_change", 0)),
                abs(spoof_details.get("ask_qty_change", 0))
            ),
            "price_impact_rupees": spoof_details.get("price_impact", 0),
            "lot_size_multiplier": 50,  # Standard lot size
            "profit_calculation": "quantity_impact × price_impact × lot_size",
            "confidence_factors": {
                "quantity_size": "Large" if max(abs(spoof_details.get("qty_spike", 0)), abs(spoof_details.get("bid_qty_change", 0))) > self.qty_threshold * 2 else "Medium",
                "time_window": "Fast" if spoof_details.get("time_window", 0) < 10 else "Normal",
                "price_impact": "High" if spoof_details.get("price_impact", 0) > 0.05 else "Low"
            }
        }

        # Calculate actual market microstructure evidence (The Architect's Fix #4)
        signal = ManipulationSignal(
            pattern_type=PatternType.ORDER_SPOOFING,
            timestamp=current['timestamp'],
            symbols_affected=[f"{symbol}_{strike}_{option_type}"],
            description=description,

            # ACTUAL MARKET EVIDENCE - no more fantasy confidence scores
            oi_change_pct_1min=self._calculate_oi_change_pct(current, next_row),
            iv_spike_z_score=self._calculate_iv_spike_z_score(current, next_row),
            bid_ask_spread_pct=self._calculate_spread_pct(current),
            traded_volume_vs_avg=self._calculate_volume_vs_avg(current),
            order_book_imbalance_ratio=self._calculate_order_book_imbalance(current),

            # Price impact evidence
            price_change_bps=abs(spoof_details.get("price_impact", 0)) * 10000,  # Convert to basis points
            time_window_seconds=spoof_details.get("time_window", 0),

            # Metadata
            detection_algorithm="spoofing_detector",
            raw_data=spoof_details
        )

        # Log the signal with structured logging (The Architect's Fix #5)
        from utils.structured_logging import structured_logger, log_manipulation_signal

        log_manipulation_signal(structured_logger, {
            "pattern_type": signal.pattern_type.value,
            "symbols_affected": signal.symbols_affected,
            "oi_change_pct_1min": signal.oi_change_pct_1min,
            "iv_spike_z_score": signal.iv_spike_z_score,
            "bid_ask_spread_pct": signal.bid_ask_spread_pct,
            "traded_volume_vs_avg": signal.traded_volume_vs_avg,
            "order_book_imbalance_ratio": signal.order_book_imbalance_ratio,
            "price_change_bps": signal.price_change_bps,
            "time_window_seconds": signal.time_window_seconds
        })

        return signal
    
    def _calculate_confidence(
        self, 
        spoof_details: Dict[str, Any], 
        current: pd.Series, 
        next_row: pd.Series
    ) -> float:
        """
        Calculate confidence score for spoofing detection
        
        Args:
            spoof_details: Spoofing pattern details
            current: Current data point
            next_row: Next data point
            
        Returns:
            Confidence score between 0 and 1
        """
        confidence = self.confidence_base
        
        # Increase confidence based on quantity size
        max_qty_change = max(
            abs(spoof_details.get("qty_spike", 0)),
            abs(spoof_details.get("bid_qty_change", 0)),
            abs(spoof_details.get("ask_qty_change", 0))
        )
        
        if max_qty_change > self.qty_threshold * 2:
            confidence += 0.1
        if max_qty_change > self.qty_threshold * 5:
            confidence += 0.1
        
        # Increase confidence based on price impact
        price_impact = spoof_details.get("price_impact", 0)
        if price_impact > 0.05:  # 5% price impact
            confidence += 0.1
        if price_impact > 0.1:   # 10% price impact
            confidence += 0.1
        
        # Increase confidence for shorter time windows (faster spoofing)
        time_window = spoof_details.get("time_window", float('inf'))
        if time_window < 10:  # Less than 10 seconds
            confidence += 0.1
        if time_window < 5:   # Less than 5 seconds
            confidence += 0.05
        
        return min(confidence, 0.95)  # Cap at 95%

    def _calculate_oi_change_pct(self, current: pd.Series, next_row: pd.Series) -> float:
        """Calculate open interest change percentage in 1 minute"""
        try:
            if current['open_interest'] > 0:
                oi_change = (next_row['open_interest'] - current['open_interest']) / current['open_interest']
                return oi_change * 100  # Convert to percentage
        except:
            pass
        return 0.0

    def _calculate_iv_spike_z_score(self, current: pd.Series, next_row: pd.Series) -> float:
        """Calculate implied volatility spike Z-score (simplified)"""
        try:
            # Simplified IV calculation based on price volatility
            price_volatility = abs(next_row['last_price'] - current['last_price']) / current['last_price']
            # Assume normal IV is around 20%, calculate Z-score
            normal_iv = 0.20
            z_score = (price_volatility - normal_iv) / (normal_iv * 0.5)  # Assume std dev is 50% of normal
            return max(z_score, 0.0)
        except:
            pass
        return 0.0

    def _calculate_spread_pct(self, current: pd.Series) -> float:
        """Calculate bid-ask spread as percentage of mid price"""
        try:
            if current['bid_price'] > 0 and current['ask_price'] > 0:
                spread = current['ask_price'] - current['bid_price']
                mid_price = (current['bid_price'] + current['ask_price']) / 2
                return (spread / mid_price) * 100  # Convert to percentage
        except:
            pass
        return 0.0

    def _calculate_volume_vs_avg(self, current: pd.Series) -> float:
        """Calculate current volume vs average (simplified)"""
        try:
            # Simplified: assume average volume is current volume / 2
            avg_volume = max(current['volume'] / 2, 1)
            return current['volume'] / avg_volume
        except:
            pass
        return 1.0

    def _calculate_order_book_imbalance(self, current: pd.Series) -> float:
        """Calculate order book imbalance ratio"""
        try:
            total_qty = current['bid_qty'] + current['ask_qty']
            if total_qty > 0:
                imbalance = (current['bid_qty'] - current['ask_qty']) / total_qty
                return imbalance
        except:
            pass
        return 0.0
    
    def _estimate_profit(self, spoof_details: Dict[str, Any], current: pd.Series) -> float:
        """
        Estimate potential profit from spoofing activity
        
        Args:
            spoof_details: Spoofing pattern details
            current: Current data point
            
        Returns:
            Estimated profit in rupees
        """
        # Base calculation: quantity impact * price impact * lot size
        qty_impact = max(
            abs(spoof_details.get("qty_spike", 0)),
            abs(spoof_details.get("bid_qty_change", 0)),
            abs(spoof_details.get("ask_qty_change", 0))
        )
        
        price_impact = spoof_details.get("price_impact", 0)
        lot_size = 50  # Typical lot size for index options
        
        # Rough estimate: quantity * price impact * lot size
        estimated_profit = qty_impact * price_impact * lot_size
        
        return max(estimated_profit, 0)
    
    def _create_description(
        self, 
        spoof_type: str, 
        spoof_details: Dict[str, Any], 
        symbol: str, 
        strike: float, 
        option_type: str
    ) -> str:
        """
        Create human-readable description of spoofing pattern
        
        Args:
            spoof_type: Type of spoofing
            spoof_details: Spoofing details
            symbol: Symbol name
            strike: Strike price
            option_type: Option type
            
        Returns:
            Description string
        """
        time_window = spoof_details.get("time_window", 0)
        
        if spoof_type == "bid_spoofing":
            qty_spike = spoof_details.get("qty_spike", 0)
            return f"Bid spoofing detected: {qty_spike:,.0f} lots added and removed within {time_window:.1f}s on {symbol} {strike} {option_type}"
        
        elif spoof_type == "ask_spoofing":
            qty_spike = spoof_details.get("qty_spike", 0)
            return f"Ask spoofing detected: {qty_spike:,.0f} lots added and removed within {time_window:.1f}s on {symbol} {strike} {option_type}"
        
        elif spoof_type == "coordinated_spoofing":
            return f"Coordinated bid-ask spoofing detected within {time_window:.1f}s on {symbol} {strike} {option_type}"
        
        else:
            return f"Order spoofing detected on {symbol} {strike} {option_type}"
