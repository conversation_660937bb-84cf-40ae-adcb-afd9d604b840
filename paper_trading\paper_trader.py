#!/usr/bin/env python3
"""
Real-time Paper Trading System for Options Manipulation Detection
Simulates trades based on detected manipulation signals and tracks P&L
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import json
import uuid

from models.data_models import ManipulationSignal, OptionsData, PatternType
from utils.execution_cost_model import execution_cost_model, OrderType
from utils.latency_tracker import latency_tracker

logger = logging.getLogger(__name__)

class TradeAction(str, Enum):
    """Trade actions based on manipulation signals"""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"

class TradeStatus(str, Enum):
    """Trade execution status"""
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    EXPIRED = "EXPIRED"

@dataclass
class PaperTrade:
    """Represents a paper trade based on manipulation signal"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    signal_id: str = ""
    symbol: str = ""
    strike: float = 0.0
    option_type: str = ""
    action: TradeAction = TradeAction.HOLD
    entry_price: float = 0.0
    exit_price: float = 0.0
    quantity: int = 0
    entry_time: datetime = field(default_factory=datetime.now)
    exit_time: Optional[datetime] = None
    status: TradeStatus = TradeStatus.OPEN
    profit_loss: float = 0.0
    profit_loss_percent: float = 0.0
    confidence: float = 0.0
    manipulation_type: str = ""
    estimated_profit: float = 0.0
    actual_profit: float = 0.0
    trade_reason: str = ""
    market_data: Dict[str, Any] = field(default_factory=dict)
    execution_costs: Dict[str, Any] = field(default_factory=dict)
    realistic_entry_price: float = 0.0
    realistic_exit_price: float = 0.0

class PaperTradingEngine:
    """
    Real-time paper trading engine that executes trades based on manipulation signals
    """
    
    def __init__(self, initial_capital: float = 1000000.0):  # ₹10 lakh starting capital
        # CRITICAL: Thread-safe locks for all shared mutable state (The Code Auditor's Directive #1)
        self._trades_lock = asyncio.Lock()
        self._capital_lock = asyncio.Lock()
        self._stats_lock = asyncio.Lock()

        # Capital tracking (protected by _capital_lock)
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.available_capital = initial_capital
        self.total_profit_loss = 0.0

        # Trade tracking (protected by _trades_lock)
        self.open_trades: Dict[str, PaperTrade] = {}
        self.closed_trades: List[PaperTrade] = []

        # Statistics (protected by _stats_lock)
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.daily_trade_count = 0

        # Trading parameters (immutable after initialization)
        self.max_position_size = 0.005  # Max 0.5% of capital per trade (The Architect's requirement)
        self.stop_loss_percent = 0.10   # 10% stop loss (tighter for front-running)
        self.take_profit_percent = 0.15  # 15% take profit (realistic for front-running)
        self.min_flow_strength = 0.3     # Minimum institutional flow strength to trade
        self.max_daily_trades = 10       # Limit daily trades to avoid overtrading
        self.breakeven_win_rate = 0.0    # Will be calculated based on costs
        
    async def process_manipulation_signal(self, signal: ManipulationSignal, current_market_data: List[OptionsData]) -> Optional[PaperTrade]:
        """
        Process a manipulation signal and decide whether to execute a paper trade
        
        Args:
            signal: Detected manipulation signal
            current_market_data: Current market data for pricing
            
        Returns:
            PaperTrade if trade was executed, None otherwise
        """
        try:
            # Measure trade decision latency
            async with latency_tracker.measure_async('trade_decision', {'signal_id': signal.id}):
                trade_decision = self._analyze_signal_for_trade(signal)

            if trade_decision["should_trade"]:
                # Find relevant market data for the signal
                relevant_option = self._find_relevant_option(signal, current_market_data)

                if relevant_option:
                    # Execute paper trade with order submission latency tracking
                    async with latency_tracker.measure_async('order_submission', {'symbol': relevant_option.symbol}):
                        trade = await self._execute_paper_trade(signal, relevant_option, trade_decision)

                    if trade:
                        logger.info(f"PAPER TRADE EXECUTED: {trade.action} {trade.symbol} at Rs{trade.entry_price}")
                        return trade
                        
        except Exception as e:
            logger.error(f"Error processing manipulation signal for trading: {str(e)}")
            
        return None
    
    def _analyze_signal_for_trade(self, signal: ManipulationSignal) -> Dict[str, Any]:
        """
        Analyze manipulation signal to detect institutional flow and front-run it

        Args:
            signal: Manipulation signal to analyze

        Returns:
            Dictionary with trading decision and strategy
        """
        decision = {
            "should_trade": False,
            "action": TradeAction.HOLD,
            "confidence_threshold": 0.85,  # Higher threshold for front-running
            "position_size_multiplier": 0.5,  # Conservative sizing
            "reason": "",
            "institutional_flow_detected": False,
            "flow_direction": None,
            "flow_strength": 0.0
        }

        # Only trade on very high confidence signals for front-running
        if signal.confidence < decision["confidence_threshold"]:
            decision["reason"] = f"Insufficient confidence for front-running: {signal.confidence:.1%}"
            return decision

        # Analyze spoofing patterns to detect institutional flow direction
        if signal.pattern_type == PatternType.ORDER_SPOOFING:
            flow_analysis = self._detect_institutional_flow(signal)

            if flow_analysis["flow_detected"]:
                decision["institutional_flow_detected"] = True
                decision["flow_direction"] = flow_analysis["direction"]
                decision["flow_strength"] = flow_analysis["strength"]
                decision["action"] = TradeAction.BUY if flow_analysis["direction"] == "bullish" else TradeAction.SELL
                decision["should_trade"] = True
                decision["reason"] = f"Institutional {flow_analysis['direction']} flow detected via {flow_analysis['spoof_type']} (strength: {flow_analysis['strength']:.1%})"

                # Adjust position size based on flow strength
                decision["position_size_multiplier"] = min(0.5 * flow_analysis["strength"], 0.5)  # Max 0.5% risk
            else:
                decision["reason"] = f"Spoofing detected but no clear institutional flow: {flow_analysis['reason']}"

        return decision

    def _detect_institutional_flow(self, signal: ManipulationSignal) -> Dict[str, Any]:
        """
        Detect institutional flow direction from spoofing patterns

        The key insight: Spoofing is often used to hide institutional flow.
        - Bid spoofing (fake bids) often precedes institutional SELLING
        - Ask spoofing (fake asks) often precedes institutional BUYING
        - Coordinated spoofing indicates major institutional activity

        Args:
            signal: Manipulation signal containing spoofing data

        Returns:
            Dictionary with flow analysis
        """
        flow_analysis = {
            "flow_detected": False,
            "direction": None,  # "bullish" or "bearish"
            "strength": 0.0,    # 0.0 to 1.0
            "spoof_type": "",
            "reason": ""
        }

        try:
            market_impact = signal.market_impact
            spoof_type = market_impact.get("spoof_type", "")
            quantity_impact = market_impact.get("quantity_impact", 0)
            price_impact = market_impact.get("price_impact", 0)
            time_window = market_impact.get("time_window", 0)

            # Calculate flow strength based on spoofing characteristics
            strength_factors = []

            # Factor 1: Quantity impact (larger spoofs indicate bigger institutional flow)
            if quantity_impact > 5000:  # Very large spoof
                strength_factors.append(0.4)
            elif quantity_impact > 2000:  # Large spoof
                strength_factors.append(0.3)
            elif quantity_impact > 1000:  # Medium spoof
                strength_factors.append(0.2)
            else:
                strength_factors.append(0.1)

            # Factor 2: Price impact (higher impact = more urgent institutional need)
            if price_impact > 0.1:  # >10% price impact
                strength_factors.append(0.3)
            elif price_impact > 0.05:  # >5% price impact
                strength_factors.append(0.2)
            elif price_impact > 0.02:  # >2% price impact
                strength_factors.append(0.1)
            else:
                strength_factors.append(0.05)

            # Factor 3: Time window (faster spoofing = more urgent flow)
            if time_window < 5:  # Very fast spoofing
                strength_factors.append(0.2)
            elif time_window < 15:  # Fast spoofing
                strength_factors.append(0.15)
            elif time_window < 30:  # Normal spoofing
                strength_factors.append(0.1)
            else:
                strength_factors.append(0.05)

            # Factor 4: Signal confidence
            strength_factors.append(signal.confidence * 0.1)

            total_strength = sum(strength_factors)

            # Determine flow direction based on spoof type
            if "bid_spoofing" in spoof_type:
                # Bid spoofing often hides institutional selling
                # Fake bids are placed to support price while institutions sell
                flow_analysis.update({
                    "flow_detected": True,
                    "direction": "bearish",
                    "strength": total_strength,
                    "spoof_type": "bid_spoofing",
                    "reason": "Bid spoofing detected - likely hiding institutional selling pressure"
                })

            elif "ask_spoofing" in spoof_type:
                # Ask spoofing often hides institutional buying
                # Fake asks are placed to suppress price while institutions buy
                flow_analysis.update({
                    "flow_detected": True,
                    "direction": "bullish",
                    "strength": total_strength,
                    "spoof_type": "ask_spoofing",
                    "reason": "Ask spoofing detected - likely hiding institutional buying pressure"
                })

            elif "coordinated_spoofing" in spoof_type:
                # Coordinated spoofing is more complex - analyze price impact direction
                if price_impact > 0:
                    # Price moved up despite spoofing - strong buying pressure
                    flow_analysis.update({
                        "flow_detected": True,
                        "direction": "bullish",
                        "strength": total_strength * 1.2,  # Boost for coordinated activity
                        "spoof_type": "coordinated_spoofing",
                        "reason": "Coordinated spoofing with upward price pressure - strong institutional buying"
                    })
                else:
                    # Price moved down - selling pressure
                    flow_analysis.update({
                        "flow_detected": True,
                        "direction": "bearish",
                        "strength": total_strength * 1.2,
                        "spoof_type": "coordinated_spoofing",
                        "reason": "Coordinated spoofing with downward price pressure - strong institutional selling"
                    })
            else:
                flow_analysis["reason"] = f"Unknown spoofing type: {spoof_type}"

            # Apply minimum strength threshold
            if flow_analysis["strength"] < 0.3:
                flow_analysis["flow_detected"] = False
                flow_analysis["reason"] += " (insufficient flow strength)"

        except Exception as e:
            flow_analysis["reason"] = f"Error analyzing institutional flow: {str(e)}"

        return flow_analysis
    
    def _find_relevant_option(self, signal: ManipulationSignal, market_data: List[OptionsData]) -> Optional[OptionsData]:
        """
        Find the relevant option from market data based on the signal
        
        Args:
            signal: Manipulation signal
            market_data: Current market data
            
        Returns:
            Relevant OptionsData or None
        """
        if not signal.symbols_affected:
            return None
            
        # Parse symbol from signal (format: "NIFTY_25000.0_CE")
        try:
            symbol_parts = signal.symbols_affected[0].split('_')
            if len(symbol_parts) >= 3:
                symbol = symbol_parts[0]
                strike = float(symbol_parts[1])
                option_type = symbol_parts[2]
                
                # Find matching option in market data
                for option in market_data:
                    if (option.symbol == symbol and 
                        option.strike == strike and 
                        option.option_type.value == option_type):
                        return option
                        
        except Exception as e:
            logger.error(f"Error parsing signal symbol: {str(e)}")
            
        return None
    
    async def _execute_paper_trade(self, signal: ManipulationSignal, option: OptionsData, decision: Dict[str, Any]) -> Optional[PaperTrade]:
        """
        Execute a paper trade based on the signal and decision with realistic front-running constraints

        Args:
            signal: Manipulation signal
            option: Option to trade
            decision: Trading decision

        Returns:
            PaperTrade if executed successfully
        """
        try:
            # Check daily trade limit
            if self.daily_trade_count >= self.max_daily_trades:
                logger.warning(f"Daily trade limit reached ({self.max_daily_trades}), skipping trade")
                return None

            # Check minimum flow strength for front-running
            if decision.get("flow_strength", 0) < self.min_flow_strength:
                logger.warning(f"Insufficient institutional flow strength: {decision.get('flow_strength', 0):.1%}")
                return None

            # Calculate ultra-conservative position size (0.5% max risk as per The Architect)
            risk_amount = self.current_capital * self.max_position_size  # 0.5% of capital

            # Calculate position size based on stop loss to limit actual risk
            stop_loss_distance = option.last_price * self.stop_loss_percent
            max_quantity_by_risk = int(risk_amount / (stop_loss_distance * 50))  # 50 is lot size

            # Additional constraints for front-running
            # 1. Limit by available capital (max 5% of available capital per trade)
            max_quantity_by_capital = int((self.available_capital * 0.05) / (option.last_price * 50))

            # 2. Limit by flow strength (weaker flow = smaller position)
            flow_strength = decision.get("flow_strength", 0.5)
            flow_adjusted_quantity = int(max_quantity_by_risk * flow_strength)

            # 3. Limit by option liquidity (don't trade more than 10% of daily volume)
            daily_volume_estimate = option.volume * 6  # Assume current volume is 1/6 of daily
            max_quantity_by_liquidity = max(1, int(daily_volume_estimate * 0.1 / 50))  # 10% of daily volume

            # Take the most conservative limit
            quantity = min(
                max_quantity_by_risk,
                max_quantity_by_capital,
                flow_adjusted_quantity,
                max_quantity_by_liquidity
            )

            if quantity <= 0:
                logger.warning("Calculated quantity is 0 after risk management, skipping trade")
                return None

            # Validate trade before execution
            validation_result = self._validate_trade_risk(option, quantity, decision)
            if not validation_result["valid"]:
                logger.warning(f"Trade validation failed: {validation_result['reason']}")
                return None

            # Calculate realistic execution costs with front-running penalties
            is_buy = decision["action"] == TradeAction.BUY
            execution_costs = execution_cost_model.calculate_execution_costs(
                option, quantity, OrderType.MARKET, is_buy
            )

            # Add front-running penalty (additional slippage due to speed requirements)
            front_running_penalty = option.last_price * 0.002  # 0.2% additional slippage
            execution_costs.effective_price += front_running_penalty if is_buy else -front_running_penalty
            execution_costs.total_cost += front_running_penalty * quantity * 50

            # Use realistic entry price (includes all costs)
            realistic_entry_price = execution_costs.effective_price

            # Check if we have enough capital (including execution costs)
            total_trade_cost = quantity * realistic_entry_price * 50
            if total_trade_cost > self.available_capital:
                logger.warning(
                    f"Insufficient capital for realistic trade: need ₹{total_trade_cost:,.0f}, "
                    f"have ₹{self.available_capital:,.0f} (execution costs: ₹{execution_costs.total_cost:,.0f})"
                )
                return None
            
            # Create paper trade with realistic pricing
            trade = PaperTrade(
                signal_id=signal.id,
                symbol=f"{option.symbol}_{option.strike}_{option.option_type.value}",
                strike=option.strike,
                option_type=option.option_type.value,
                action=decision["action"],
                entry_price=option.last_price,  # Theoretical price
                realistic_entry_price=realistic_entry_price,  # Actual executable price
                quantity=quantity,
                entry_time=datetime.now(),
                confidence=signal.confidence,
                manipulation_type=signal.pattern_type.value,
                estimated_profit=signal.estimated_profit,
                trade_reason=decision["reason"],
                market_data={
                    "bid_price": option.bid_price,
                    "ask_price": option.ask_price,
                    "volume": option.volume,
                    "open_interest": option.open_interest
                },
                execution_costs=execution_cost_model.get_cost_summary(execution_costs)
            )
            
            # Update capital allocation (use realistic cost)
            self.available_capital -= total_trade_cost
            self.open_trades[trade.id] = trade
            self.total_trades += 1

            # Store trade in database
            await self._store_trade_in_database(trade)

            logger.info(
                f"Paper trade executed: {trade.action} {quantity} lots of {trade.symbol} "
                f"at Rs{trade.realistic_entry_price:.2f} (theoretical: Rs{trade.entry_price:.2f}, "
                f"costs: Rs{execution_costs.total_cost:.0f})"
            )

            return trade
            
        except Exception as e:
            logger.error(f"Error executing paper trade: {str(e)}")
            return None
    
    async def update_open_trades(self, current_market_data: List[OptionsData]):
        """
        Update open trades with current market prices and check for exit conditions
        THREAD-SAFE IMPLEMENTATION - The Code Auditor's Directive #1

        Args:
            current_market_data: Current market data for pricing
        """
        # CRITICAL: Create a copy of trades to avoid race condition during iteration
        async with self._trades_lock:
            trades_copy = dict(self.open_trades)

        trades_to_close = []

        # Process trades outside the lock to minimize lock contention
        for trade_id, trade in trades_copy.items():
            try:
                # Find current price for this option
                current_option = self._find_option_by_symbol(trade.symbol, current_market_data)
                
                if current_option:
                    current_price = current_option.last_price
                    
                    # Calculate current P&L
                    if trade.action == TradeAction.BUY:
                        pnl = (current_price - trade.entry_price) * trade.quantity * 50
                        pnl_percent = (current_price - trade.entry_price) / trade.entry_price
                    else:  # SELL
                        pnl = (trade.entry_price - current_price) * trade.quantity * 50
                        pnl_percent = (trade.entry_price - current_price) / trade.entry_price
                    
                    trade.profit_loss = pnl
                    trade.profit_loss_percent = pnl_percent
                    
                    # Check exit conditions
                    should_close = False
                    close_reason = ""
                    
                    # Stop loss check
                    if pnl_percent <= -self.stop_loss_percent:
                        should_close = True
                        close_reason = f"Stop loss hit: {pnl_percent:.1%}"
                    
                    # Take profit check
                    elif pnl_percent >= self.take_profit_percent:
                        should_close = True
                        close_reason = f"Take profit hit: {pnl_percent:.1%}"
                    
                    # Time-based exit (close after 1 hour)
                    elif datetime.now() - trade.entry_time > timedelta(hours=1):
                        should_close = True
                        close_reason = "Time-based exit (1 hour)"
                    
                    if should_close:
                        trade.exit_price = current_price
                        trade.exit_time = datetime.now()
                        trade.status = TradeStatus.CLOSED
                        trade.actual_profit = pnl
                        trades_to_close.append(trade_id)
                        
                        logger.info(f"🔄 Closing trade: {trade.symbol} - {close_reason} - P&L: ₹{pnl:,.0f}")
                        
            except Exception as e:
                logger.error(f"Error updating trade {trade_id}: {str(e)}")
        
        # Close trades that met exit conditions with atomic operations
        for trade_id in trades_to_close:
            await self._close_trade_atomic(trade_id)
    
    def _find_option_by_symbol(self, symbol: str, market_data: List[OptionsData]) -> Optional[OptionsData]:
        """Find option in market data by symbol string"""
        try:
            parts = symbol.split('_')
            if len(parts) >= 3:
                sym = parts[0]
                strike = float(parts[1])
                opt_type = parts[2]
                
                for option in market_data:
                    if (option.symbol == sym and 
                        option.strike == strike and 
                        option.option_type.value == opt_type):
                        return option
        except:
            pass
        return None
    
    async def _close_trade_atomic(self, trade_id: str):
        """
        Close a trade and update statistics with atomic operations
        THREAD-SAFE IMPLEMENTATION - The Code Auditor's Directive #1
        """
        # First, get the trade under lock
        async with self._trades_lock:
            if trade_id not in self.open_trades:
                return
            trade = self.open_trades[trade_id]

        # Atomic capital updates
        async with self._capital_lock:
            trade_value = trade.quantity * trade.exit_price * 50
            self.available_capital += trade_value
            self.current_capital += trade.actual_profit
            self.total_profit_loss += trade.actual_profit

        # Atomic statistics updates
        async with self._stats_lock:
            if trade.actual_profit > 0:
                self.winning_trades += 1
            else:
                self.losing_trades += 1

        # Atomic trade list updates
        async with self._trades_lock:
            if trade_id in self.open_trades:  # Double-check under lock
                self.closed_trades.append(trade)
                del self.open_trades[trade_id]

        # Update database (outside locks to avoid blocking)
        await self._update_trade_in_database(trade)
    
    async def _store_trade_in_database(self, trade: PaperTrade):
        """Store paper trade in database"""
        try:
            # Implementation would store trade in database
            # For now, just log it
            logger.info(f"Storing trade in database: {trade.id}")
        except Exception as e:
            logger.error(f"Error storing trade in database: {str(e)}")
    
    async def _update_trade_in_database(self, trade: PaperTrade):
        """Update paper trade in database"""
        try:
            # Implementation would update trade in database
            logger.info(f"📊 Updating trade in database: {trade.id} - P&L: ₹{trade.actual_profit:,.0f}")
        except Exception as e:
            logger.error(f"Error updating trade in database: {str(e)}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        total_trades = len(self.closed_trades)
        win_rate = (self.winning_trades / max(total_trades, 1)) * 100
        
        # Calculate realistic breakeven win rate
        breakeven_rate = self.calculate_breakeven_win_rate()

        return {
            "initial_capital": self.initial_capital,
            "current_capital": self.current_capital,
            "available_capital": self.available_capital,
            "total_profit_loss": self.total_profit_loss,
            "total_profit_loss_percent": (self.total_profit_loss / self.initial_capital) * 100,
            "total_trades": total_trades,
            "open_trades": len(self.open_trades),
            "winning_trades": self.winning_trades,
            "losing_trades": self.losing_trades,
            "win_rate": win_rate,
            "breakeven_win_rate": breakeven_rate,
            "win_rate_vs_breakeven": win_rate - breakeven_rate,
            "average_profit_per_trade": self.total_profit_loss / max(total_trades, 1),
            "best_trade": max([t.actual_profit for t in self.closed_trades], default=0),
            "worst_trade": min([t.actual_profit for t in self.closed_trades], default=0),
            "current_drawdown": min(0, self.current_capital - self.initial_capital),
            "max_capital": max(self.current_capital, self.initial_capital),
            "daily_trade_count": self.daily_trade_count,
            "max_daily_trades": self.max_daily_trades,
            "position_size_percent": self.max_position_size * 100,
            "front_running_metrics": {
                "min_flow_strength": self.min_flow_strength,
                "stop_loss_percent": self.stop_loss_percent,
                "take_profit_percent": self.take_profit_percent
            }
        }

    def calculate_breakeven_win_rate(self) -> float:
        """
        Calculate the minimum win rate needed to break even given execution costs

        This is critical for front-running strategies where costs are high

        Returns:
            Breakeven win rate as a percentage
        """
        try:
            # Realistic execution costs for NSE options front-running
            base_execution_cost_pct = 0.015  # 1.5% base execution cost
            front_running_penalty_pct = 0.003  # 0.3% additional slippage for speed
            market_impact_pct = 0.002  # 0.2% market impact

            # Total one-way cost
            one_way_cost_pct = base_execution_cost_pct + front_running_penalty_pct + market_impact_pct

            # Round trip cost (entry + exit)
            total_cost_pct = one_way_cost_pct * 2  # = 4% total

            # Front-running strategy parameters (more realistic for short-term trades)
            avg_win_pct = self.take_profit_percent  # 15% target
            avg_loss_pct = self.stop_loss_percent   # 10% stop loss

            # For front-running, we need to account for the fact that:
            # 1. Wins are often smaller due to quick exits
            # 2. Losses can be larger due to adverse selection
            # 3. Hit rate needs to be very high due to costs

            # Adjusted win/loss for front-running reality
            realistic_avg_win = avg_win_pct * 0.7  # Often exit early = 10.5%
            realistic_avg_loss = avg_loss_pct * 1.2  # Sometimes worse than stop = 12%

            # Breakeven calculation with costs:
            # win_rate * realistic_avg_win = (1 - win_rate) * realistic_avg_loss + total_cost_pct
            # Solving for win_rate:
            # win_rate = (realistic_avg_loss + total_cost_pct) / (realistic_avg_win + realistic_avg_loss)

            breakeven_rate = (realistic_avg_loss + total_cost_pct) / (realistic_avg_win + realistic_avg_loss)

            # The Architect's reality check: front-running needs 80%+ win rate
            return min(max(breakeven_rate * 100, 80.0), 95.0)  # Minimum 80%, cap at 95%

        except Exception as e:
            logger.error(f"Error calculating breakeven win rate: {str(e)}")
            return 85.0  # Conservative fallback estimate

    def _validate_trade_risk(self, option: OptionsData, quantity: int, decision: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate trade against multiple risk criteria before execution

        Args:
            option: Option to trade
            quantity: Intended quantity
            decision: Trading decision with flow analysis

        Returns:
            Dictionary with validation result
        """
        validation = {"valid": True, "reason": "", "warnings": []}

        try:
            # 1. Check if we're exceeding position concentration limits
            total_position_value = quantity * option.last_price * 50
            position_concentration = (total_position_value / self.current_capital) * 100

            if position_concentration > 0.5:  # More than 0.5% of capital
                validation["valid"] = False
                validation["reason"] = f"Position concentration too high: {position_concentration:.2f}%"
                return validation

            # 2. Check daily trade frequency (avoid overtrading)
            if self.daily_trade_count >= self.max_daily_trades:
                validation["valid"] = False
                validation["reason"] = f"Daily trade limit exceeded: {self.daily_trade_count}/{self.max_daily_trades}"
                return validation

            # 3. Check option liquidity (avoid illiquid options)
            if option.volume < 50:  # Less than 50 lots traded
                validation["warnings"].append("Low liquidity option - execution may be difficult")
                if option.volume < 10:  # Very low liquidity
                    validation["valid"] = False
                    validation["reason"] = f"Insufficient liquidity: {option.volume} lots"
                    return validation

            # 4. Check bid-ask spread (avoid wide spreads)
            if option.bid_price > 0 and option.ask_price > 0:
                spread_pct = ((option.ask_price - option.bid_price) / option.last_price) * 100
                if spread_pct > 5.0:  # More than 5% spread
                    validation["warnings"].append(f"Wide spread: {spread_pct:.1f}%")
                    if spread_pct > 10.0:  # Very wide spread
                        validation["valid"] = False
                        validation["reason"] = f"Spread too wide: {spread_pct:.1f}%"
                        return validation

            # 5. Check institutional flow strength for front-running
            flow_strength = decision.get("flow_strength", 0)
            if flow_strength < self.min_flow_strength:
                validation["valid"] = False
                validation["reason"] = f"Insufficient institutional flow: {flow_strength:.1%}"
                return validation

            # 6. Check time to expiry (avoid options expiring soon)
            if hasattr(option, 'expiry_date') and option.expiry_date:
                days_to_expiry = (option.expiry_date - datetime.now().date()).days
                if days_to_expiry < 7:  # Less than 1 week
                    validation["warnings"].append(f"Option expires in {days_to_expiry} days")
                    if days_to_expiry < 2:  # Less than 2 days
                        validation["valid"] = False
                        validation["reason"] = f"Option expires too soon: {days_to_expiry} days"
                        return validation

            # 7. Check available capital
            required_capital = total_position_value * 1.2  # 20% buffer for costs
            if required_capital > self.available_capital:
                validation["valid"] = False
                validation["reason"] = f"Insufficient capital: need ₹{required_capital:,.0f}, have ₹{self.available_capital:,.0f}"
                return validation

            # 8. Check maximum open positions
            if len(self.open_trades) >= 5:  # Max 5 open positions
                validation["valid"] = False
                validation["reason"] = f"Too many open positions: {len(self.open_trades)}/5"
                return validation

            # Log warnings if any
            if validation["warnings"]:
                for warning in validation["warnings"]:
                    logger.warning(f"Trade validation warning: {warning}")

        except Exception as e:
            validation["valid"] = False
            validation["reason"] = f"Validation error: {str(e)}"

        return validation

# Global paper trading engine instance
paper_trading_engine = PaperTradingEngine()
