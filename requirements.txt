# Core dependencies - actually used in codebase
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.11.0
requests>=2.31.0
aiohttp>=3.8.0

# Database and caching - used for data storage
sqlalchemy>=2.0.0
psycopg2-binary>=2.9.0
asyncpg>=0.28.0
aiosqlite>=0.19.0

# Data validation and configuration - core to the system
pydantic>=2.0.0
pydantic-settings>=2.0.0
python-dotenv>=1.0.0

# API framework - used for REST API
fastapi>=0.100.0
uvicorn>=0.23.0

# Monitoring - used for metrics collection
prometheus-client>=0.17.0

# Testing - essential for validation
pytest>=7.4.0
pytest-asyncio>=0.21.0
httpx>=0.24.0
