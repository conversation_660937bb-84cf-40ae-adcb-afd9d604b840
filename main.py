"""
Main entry point for the Options Manipulation Detection System
The Architect's Fix #3: Fail-fast logic with structured logging
"""
import asyncio
import logging
import signal
import sys
import os
from typing import Optional

# Set UTF-8 encoding for Windows console
if sys.platform == "win32":
    os.environ["PYTHONIOENCODING"] = "utf-8"

# Import structured logging FIRST (The Architect's Fix #5)
from utils.structured_logging import (
    structured_logger,
    log_system_startup,
    log_fatal_error
)

# Import core components
from core.detection_engine import detection_engine
from config.settings import settings

class OptionsDetectionSystem:
    """
    Main system orchestrator
    """
    
    def __init__(self):
        self.running = False
        self.shutdown_event = asyncio.Event()
    
    async def start(self):
        """
        Start the detection system with fail-fast logic
        The Architect's Fix #3: Monolithic health - if any critical component fails, kill everything
        """
        try:
            # Log system startup with structured logging
            log_system_startup(structured_logger, {
                "environment": settings.environment,
                "symbols": settings.symbols,
                "detection_interval": settings.detection_interval,
                "database_url": settings.database_url
            })

            # Initialize detection engine with fail-fast
            try:
                await detection_engine.initialize()
                structured_logger.info("Detection engine initialized successfully")
            except Exception as e:
                log_fatal_error(structured_logger, "detection_engine", e)
                # This will call sys.exit(1) - no return from here

            # Set up signal handlers for graceful shutdown
            self._setup_signal_handlers()

            self.running = True
            structured_logger.info("System started successfully", status="OPERATIONAL")

            # Run detection engine
            await detection_engine.run_continuous()

        except KeyboardInterrupt:
            structured_logger.info("Shutdown requested by user", reason="SIGINT")
        except Exception as e:
            # Any unhandled exception is fatal
            log_fatal_error(structured_logger, "main_system", e)
            # This will call sys.exit(1) - no return from here
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Shutdown the system gracefully with structured logging"""
        if not self.running:
            return

        structured_logger.info("Shutting down Options Manipulation Detection System", status="SHUTTING_DOWN")
        self.running = False

        try:
            # Shutdown detection engine
            await detection_engine.shutdown()

            # Set shutdown event
            self.shutdown_event.set()

            structured_logger.info("System shutdown complete", status="STOPPED")

        except Exception as e:
            structured_logger.error("Error during shutdown", error=str(e), error_type=type(e).__name__)
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            structured_logger.info("Received shutdown signal", signal=signum, action="INITIATING_SHUTDOWN")
            asyncio.create_task(self.shutdown())

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

async def run_api_server():
    """Run the API server"""
    import uvicorn
    from api.main import app
    
    config = uvicorn.Config(
        app,
        host=settings.api.host,
        port=settings.api.port,
        workers=1,  # Use 1 worker for development
        reload=settings.api.reload,
        log_level=settings.monitoring.log_level.lower()
    )
    
    server = uvicorn.Server(config)
    await server.serve()

async def run_detection_only():
    """Run only the detection engine without API"""
    system = OptionsDetectionSystem()
    await system.start()

async def run_full_system():
    """Run both detection engine and API server"""
    # Start detection engine in background
    detection_task = asyncio.create_task(run_detection_only())
    
    # Start API server
    api_task = asyncio.create_task(run_api_server())
    
    # Wait for either to complete
    done, pending = await asyncio.wait(
        [detection_task, api_task],
        return_when=asyncio.FIRST_COMPLETED
    )
    
    # Cancel remaining tasks
    for task in pending:
        task.cancel()
        try:
            await task
        except asyncio.CancelledError:
            pass

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Options Manipulation Detection System")
    parser.add_argument(
        "--mode",
        choices=["detection", "api", "full"],
        default="full",
        help="Run mode: detection only, API only, or full system"
    )
    parser.add_argument(
        "--config",
        help="Path to configuration file"
    )
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level"
    )
    
    args = parser.parse_args()
    
    # Update log level if specified
    if args.log_level:
        structured_logger.logger.setLevel(getattr(logging, args.log_level))

    # Load custom config if specified
    if args.config:
        # TODO: Implement config file loading
        structured_logger.info("Loading custom configuration", config_file=args.config)

    try:
        if args.mode == "detection":
            structured_logger.info("Starting in detection-only mode", mode="DETECTION_ONLY")
            asyncio.run(run_detection_only())
        elif args.mode == "api":
            structured_logger.info("Starting in API-only mode", mode="API_ONLY")
            asyncio.run(run_api_server())
        else:
            structured_logger.info("Starting in full system mode", mode="FULL_SYSTEM")
            asyncio.run(run_full_system())

    except KeyboardInterrupt:
        structured_logger.info("System stopped by user", reason="KEYBOARD_INTERRUPT")
    except Exception as e:
        # Fatal error - use structured logging and fail-fast
        log_fatal_error(structured_logger, "main_application", e)
        # This will call sys.exit(1) - no return from here

if __name__ == "__main__":
    main()
