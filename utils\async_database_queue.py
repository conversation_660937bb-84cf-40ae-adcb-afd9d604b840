"""
Async Database Queue
The Code Auditor's Directive #3: Decouple I/O from Critical Path

Eliminates synchronous database operations from the detection cycle
Provides batched, asynchronous database writes with backpressure handling
"""
import asyncio
import time
import logging
from typing import List, Dict, Any, Union, Optional
from dataclasses import dataclass
from enum import Enum
from collections import deque

from models.data_models import ManipulationSignal, OptionsData
from utils.database import db_manager
from utils.structured_logging import structured_logger, log_database_operation

logger = logging.getLogger(__name__)

class QueueItemType(Enum):
    SIGNAL = "signal"
    OPTIONS_DATA = "options_data"
    TRADE_UPDATE = "trade_update"

@dataclass
class QueueItem:
    item_type: QueueItemType
    data: Any
    timestamp: float
    priority: int = 0  # Higher priority = processed first

class AsyncDatabaseQueue:
    """
    High-performance async database queue with batching and backpressure
    The Code Auditor's Directive #3: Eliminate I/O from critical path
    """
    
    def __init__(self, 
                 batch_size: int = 1000,
                 flush_interval: float = 1.0,
                 max_queue_size: int = 50000,
                 max_retry_attempts: int = 3):
        """
        Initialize async database queue
        
        Args:
            batch_size: Maximum items per batch write
            flush_interval: Maximum time between flushes (seconds)
            max_queue_size: Maximum queue size before backpressure
            max_retry_attempts: Maximum retry attempts for failed writes
        """
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        self.max_queue_size = max_queue_size
        self.max_retry_attempts = max_retry_attempts
        
        # Separate queues for different data types (priority handling)
        self.signal_queue = asyncio.Queue(maxsize=max_queue_size // 4)
        self.options_queue = asyncio.Queue(maxsize=max_queue_size)
        self.trade_queue = asyncio.Queue(maxsize=max_queue_size // 10)
        
        # Worker task and control
        self.worker_task: Optional[asyncio.Task] = None
        self.running = False
        
        # Performance metrics
        self.total_items_processed = 0
        self.total_batches_written = 0
        self.total_write_time = 0.0
        self.failed_writes = 0
        
        # Backpressure tracking
        self.backpressure_events = 0
        self.queue_full_events = 0
        
    async def start(self):
        """Start the async database worker"""
        if self.running:
            return
            
        self.running = True
        self.worker_task = asyncio.create_task(self._batch_worker())
        
        structured_logger.info(
            "AsyncDatabaseQueue started",
            batch_size=self.batch_size,
            flush_interval=self.flush_interval,
            max_queue_size=self.max_queue_size
        )
    
    async def stop(self):
        """Stop the async database worker and flush remaining items"""
        if not self.running:
            return
            
        self.running = False
        
        if self.worker_task:
            # Wait for worker to finish current batch
            await asyncio.wait_for(self.worker_task, timeout=10.0)
        
        # Flush any remaining items
        await self._flush_all_queues()
        
        structured_logger.info(
            "AsyncDatabaseQueue stopped",
            total_items_processed=self.total_items_processed,
            total_batches_written=self.total_batches_written,
            avg_write_time_ms=(self.total_write_time / max(self.total_batches_written, 1)) * 1000
        )
    
    async def enqueue_signals(self, signals: List[ManipulationSignal]):
        """
        Enqueue manipulation signals for async database write
        
        Args:
            signals: List of manipulation signals to store
        """
        if not self.running:
            raise RuntimeError("AsyncDatabaseQueue not started")
        
        for signal in signals:
            try:
                # Non-blocking enqueue with backpressure handling
                self.signal_queue.put_nowait(QueueItem(
                    item_type=QueueItemType.SIGNAL,
                    data=signal,
                    timestamp=time.time(),
                    priority=1  # High priority for signals
                ))
            except asyncio.QueueFull:
                self.queue_full_events += 1
                # Apply backpressure - wait briefly and retry
                await asyncio.sleep(0.001)  # 1ms backpressure delay
                try:
                    await asyncio.wait_for(
                        self.signal_queue.put(QueueItem(
                            item_type=QueueItemType.SIGNAL,
                            data=signal,
                            timestamp=time.time(),
                            priority=1
                        )), 
                        timeout=0.1
                    )
                except asyncio.TimeoutError:
                    self.backpressure_events += 1
                    structured_logger.warning(
                        "Signal queue backpressure - dropping signal",
                        signal_id=signal.id,
                        queue_size=self.signal_queue.qsize()
                    )
    
    async def enqueue_options_data(self, options_data: List[OptionsData]):
        """
        Enqueue options data for async database write
        
        Args:
            options_data: List of options data to store
        """
        if not self.running:
            raise RuntimeError("AsyncDatabaseQueue not started")
        
        # Batch options data to reduce queue pressure
        batch_size = 100
        for i in range(0, len(options_data), batch_size):
            batch = options_data[i:i + batch_size]
            
            try:
                self.options_queue.put_nowait(QueueItem(
                    item_type=QueueItemType.OPTIONS_DATA,
                    data=batch,
                    timestamp=time.time(),
                    priority=0  # Lower priority than signals
                ))
            except asyncio.QueueFull:
                # For options data, we can afford to drop some under extreme load
                self.queue_full_events += 1
                if self.queue_full_events % 100 == 0:  # Log every 100th drop
                    structured_logger.warning(
                        "Options data queue full - dropping batch",
                        batch_size=len(batch),
                        queue_size=self.options_queue.qsize()
                    )
    
    async def _batch_worker(self):
        """
        Main worker loop for batched database writes
        Processes items from all queues with priority handling
        """
        signal_batch = []
        options_batch = []
        trade_batch = []
        last_flush = time.time()
        
        while self.running:
            try:
                # Collect items with priority: signals > trades > options
                item = await self._get_next_item_with_priority()
                
                if item:
                    if item.item_type == QueueItemType.SIGNAL:
                        signal_batch.append(item.data)
                    elif item.item_type == QueueItemType.OPTIONS_DATA:
                        options_batch.extend(item.data)  # item.data is already a batch
                    elif item.item_type == QueueItemType.TRADE_UPDATE:
                        trade_batch.append(item.data)
                
                # Flush conditions
                should_flush = (
                    len(signal_batch) >= self.batch_size or
                    len(options_batch) >= self.batch_size or
                    len(trade_batch) >= self.batch_size or
                    (time.time() - last_flush) > self.flush_interval
                )
                
                if should_flush and (signal_batch or options_batch or trade_batch):
                    await self._flush_batches(signal_batch, options_batch, trade_batch)
                    signal_batch.clear()
                    options_batch.clear()
                    trade_batch.clear()
                    last_flush = time.time()
                    
            except Exception as e:
                structured_logger.error(
                    "Database queue worker error",
                    error=str(e),
                    error_type=type(e).__name__
                )
                await asyncio.sleep(1.0)  # Brief pause on error
    
    async def _get_next_item_with_priority(self) -> Optional[QueueItem]:
        """Get next item with priority: signals > trades > options"""
        try:
            # Try signals first (highest priority)
            return await asyncio.wait_for(self.signal_queue.get(), timeout=0.01)
        except asyncio.TimeoutError:
            pass
        
        try:
            # Try trades second
            return await asyncio.wait_for(self.trade_queue.get(), timeout=0.01)
        except asyncio.TimeoutError:
            pass
        
        try:
            # Try options data last (lowest priority)
            return await asyncio.wait_for(self.options_queue.get(), timeout=0.1)
        except asyncio.TimeoutError:
            return None
    
    async def _flush_batches(self, 
                           signals: List[ManipulationSignal],
                           options: List[OptionsData], 
                           trades: List[Any]):
        """Flush all batches to database with error handling and retries"""
        start_time = time.time()
        
        # Execute all writes concurrently
        tasks = []
        
        if signals:
            tasks.append(self._write_signals_with_retry(signals))
        
        if options:
            tasks.append(self._write_options_with_retry(options))
        
        if trades:
            tasks.append(self._write_trades_with_retry(trades))
        
        if tasks:
            # Execute all writes concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Update metrics
            write_time = time.time() - start_time
            self.total_write_time += write_time
            self.total_batches_written += 1
            self.total_items_processed += len(signals) + len(options) + len(trades)
            
            # Log performance
            log_database_operation(
                structured_logger,
                operation="batch_write",
                table="multiple",
                records_affected=len(signals) + len(options) + len(trades),
                duration_ms=write_time * 1000,
                success=all(not isinstance(r, Exception) for r in results)
            )
    
    async def _write_signals_with_retry(self, signals: List[ManipulationSignal]):
        """Write signals with retry logic"""
        for attempt in range(self.max_retry_attempts):
            try:
                await db_manager.store_manipulation_signals(signals)
                return
            except Exception as e:
                if attempt == self.max_retry_attempts - 1:
                    self.failed_writes += 1
                    structured_logger.error(
                        "Failed to write signals after retries",
                        signals_count=len(signals),
                        error=str(e),
                        attempts=attempt + 1
                    )
                    raise
                await asyncio.sleep(0.1 * (2 ** attempt))  # Exponential backoff
    
    async def _write_options_with_retry(self, options: List[OptionsData]):
        """Write options data with retry logic"""
        for attempt in range(self.max_retry_attempts):
            try:
                await db_manager.store_options_data(options)
                return
            except Exception as e:
                if attempt == self.max_retry_attempts - 1:
                    self.failed_writes += 1
                    structured_logger.error(
                        "Failed to write options data after retries",
                        options_count=len(options),
                        error=str(e),
                        attempts=attempt + 1
                    )
                    raise
                await asyncio.sleep(0.1 * (2 ** attempt))
    
    async def _write_trades_with_retry(self, trades: List[Any]):
        """Write trade updates with retry logic"""
        # TODO: Implement trade database writes
        pass
    
    async def _flush_all_queues(self):
        """Flush all remaining items in queues"""
        remaining_signals = []
        remaining_options = []
        remaining_trades = []
        
        # Drain all queues
        while not self.signal_queue.empty():
            try:
                item = self.signal_queue.get_nowait()
                remaining_signals.append(item.data)
            except asyncio.QueueEmpty:
                break
        
        while not self.options_queue.empty():
            try:
                item = self.options_queue.get_nowait()
                remaining_options.extend(item.data)
            except asyncio.QueueEmpty:
                break
        
        while not self.trade_queue.empty():
            try:
                item = self.trade_queue.get_nowait()
                remaining_trades.append(item.data)
            except asyncio.QueueEmpty:
                break
        
        # Write remaining items
        if remaining_signals or remaining_options or remaining_trades:
            await self._flush_batches(remaining_signals, remaining_options, remaining_trades)
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get queue performance metrics"""
        return {
            "total_items_processed": self.total_items_processed,
            "total_batches_written": self.total_batches_written,
            "avg_write_time_ms": (self.total_write_time / max(self.total_batches_written, 1)) * 1000,
            "failed_writes": self.failed_writes,
            "backpressure_events": self.backpressure_events,
            "queue_full_events": self.queue_full_events,
            "current_queue_sizes": {
                "signals": self.signal_queue.qsize(),
                "options": self.options_queue.qsize(),
                "trades": self.trade_queue.qsize()
            },
            "running": self.running
        }

# Global async database queue instance
async_db_queue = AsyncDatabaseQueue()
